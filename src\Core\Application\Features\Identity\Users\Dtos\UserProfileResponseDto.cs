using Abstraction.Base.Dto;
using Application.Features.Identity.Users.Queries.Responses;
using Application.Features.Resolutions.Dtos;
using Application.Features.Shared.FileManagment.Dtos;

namespace Application.Features.Identity.Users.Dtos
{
    /// <summary>
    /// Response DTO for user profile information
    /// Contains all user profile data for display and editing
    /// </summary>
    public record UserProfileResponseDto :  BaseUserDto  
    {

        ///// <summary>
        ///// Personal photo file path/URL
        ///// </summary>
        //public string? PersonalPhotoPath { get; set; }

        public string? CountryCode { get; set; }

        /// <summary>
        /// User status (read-only)
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// User roles (read-only)
        /// </summary>
        public List<string> Roles { get; set; } = new();

        /// <summary>
        /// Registration completion status (read-only)
        /// </summary>
        public bool RegistrationIsCompleted { get; set; }

        /// <summary>
        /// True if there is an active user assigned to the Legal Counsel role
        /// </summary>
        public bool LegalCouncilHasActiveUser { get; set; }

        /// <summary>
        /// True if there is an active user assigned to the Finance Controller role
        /// </summary>
        public bool FinanceControllerHasActiveUser { get; set; }

        /// <summary>
        /// True if there is an active user assigned to the Compliance Legal Managing Director role
        /// </summary>
        public bool ComplianceLegalManagingDirectorHasActiveUser { get; set; }

        /// <summary>
        /// True if there is an active user assigned to the Head of Real Estate role
        /// </summary>
        public bool HeadOfRealEstateHasActiveUser { get; set; }

        /// <summary>
        /// True if there is an active user assigned to the Legal Counsel role
        /// </summary>
        public string? LegalCouncilUserFullName { get; set; }

        /// <summary>
        /// True if there is an active user assigned to the Finance Controller role
        /// </summary>
        public string? FinanceControllerUserFullName { get; set; }

        /// <summary>
        /// True if there is an active user assigned to the Compliance Legal Managing Director role
        /// </summary>
        public string? ComplianceLegalManagingDirectorUserFullName { get; set; }

        /// <summary>
        /// True if there is an active user assigned to the Head of Real Estate role
        /// </summary>
        public string? HeadOfRealEstateUserFullName { get; set; }

        public int? CvFileId { get; set; }

        public AttachmentDto CvFile { get; set; }

        /// <summary>
        /// Personal photo file ID
        /// </summary>
        public int? PersonalPhotoFileId { get; set; }

        /// <summary>
        /// Personal photo attachment details
        /// </summary>
        public AttachmentDto PersonalPhoto { get; set; }

    }
}
