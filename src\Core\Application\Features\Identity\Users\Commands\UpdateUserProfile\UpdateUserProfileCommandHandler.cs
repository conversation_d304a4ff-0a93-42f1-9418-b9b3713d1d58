using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Identity;
using Domain.Entities.Users;
using AutoMapper;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Repository;

namespace Application.Features.Identity.Users.Commands.UpdateUserProfile
{
    /// <summary>
    /// Hand<PERSON> for updating user profile information
    /// Implements Clean Architecture and CQRS patterns with file upload support
    /// </summary>
    public class UpdateUserProfileCommandHandler : BaseResponseHandler, ICommandHandler<UpdateUserProfileCommand, BaseResponse<string>>
    {
        #region Fields
 
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        private readonly IIdentityServiceManager _identityService;
        private readonly IRepositoryManager _repositoryManager;
        // TODO: Add IFileUploadService when available
        #endregion

        #region Constructor
        public UpdateUserProfileCommandHandler(
 
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            IIdentityServiceManager identityService,
            IRepositoryManager repositoryManager,
            ICurrentUserService currentUserService)
        {
 
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
            _identityService = identityService;
            _repositoryManager = repositoryManager;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(UpdateUserProfileCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Set user ID from current user context
                request.Id = _currentUserService.UserId.GetValueOrDefault();

                // Get existing user
                var user = await _identityService.UserManagmentService.FindByIdAsync(request.Id.ToString());
                if (user == null)
                {
                    return NotFound<string>(_localizer[SharedResourcesKey.UserNotFound]);
                }

                // Check email uniqueness if changed
                if (!string.Equals(user.Email, request.Email, StringComparison.OrdinalIgnoreCase))
                {
                    var existingUserWithEmail = await  _identityService.UserManagmentService.FindByEmailAsync(request.Email);
                    if (existingUserWithEmail != null && existingUserWithEmail.Id != user.Id)
                    {
                        return BadRequest<string>(_localizer[SharedResourcesKey.ProfileDuplicateEmail]);
                    }
                }

               

                // Update user properties using AutoMapper
                _mapper.Map(request, user);
                

                // Update last update date
                user.UpdatedAt = DateTime.Now;
                user.UpdatedBy = _currentUserService.UserId;
                // Save changes
                var result = await _identityService.UserManagmentService.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return BadRequest<string>($"{_localizer[SharedResourcesKey.ProfileSystemErrorSavingData]}: {errors}");
                }

                return Success<string>(_localizer[SharedResourcesKey.ProfileUpdatedSuccessfully]);
            }
            catch (Exception ex)
            {
                return ServerError<string>(_localizer[SharedResourcesKey.ProfileSystemErrorSavingData]);
            }
        }
        #endregion
    }
}
